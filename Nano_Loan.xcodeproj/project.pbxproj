// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		09598F8C78BEA3CA7BF618DB /* Pods_Nano_Loan.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1E97E9DA174BA64351BD3070 /* Pods_Nano_Loan.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0457B1452DF0C6140064E008 /* Nano_Loan.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Nano_Loan.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1E97E9DA174BA64351BD3070 /* Pods_Nano_Loan.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Nano_Loan.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		396BFFD982BA4DEEB82BEF6A /* Pods-Nano_Loan.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Nano_Loan.debug.xcconfig"; path = "Target Support Files/Pods-Nano_Loan/Pods-Nano_Loan.debug.xcconfig"; sourceTree = "<group>"; };
		BF901CC1F1C644BB2CDED1D3 /* Pods-Nano_Loan.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Nano_Loan.release.xcconfig"; path = "Target Support Files/Pods-Nano_Loan/Pods-Nano_Loan.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		0457B15C2DF0C6170064E008 /* Exceptions for "Nano_Loan" folder in "Nano_Loan" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 0457B1442DF0C6140064E008 /* Nano_Loan */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		0457B1472DF0C6140064E008 /* Nano_Loan */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				0457B15C2DF0C6170064E008 /* Exceptions for "Nano_Loan" folder in "Nano_Loan" target */,
			);
			path = Nano_Loan;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		0457B1422DF0C6140064E008 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				09598F8C78BEA3CA7BF618DB /* Pods_Nano_Loan.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0457B13C2DF0C6140064E008 = {
			isa = PBXGroup;
			children = (
				0457B1472DF0C6140064E008 /* Nano_Loan */,
				0457B1462DF0C6140064E008 /* Products */,
				C7FE12D6BEAF84BF0F750AB4 /* Pods */,
				3494F9165B68CA5434254A52 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		0457B1462DF0C6140064E008 /* Products */ = {
			isa = PBXGroup;
			children = (
				0457B1452DF0C6140064E008 /* Nano_Loan.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		3494F9165B68CA5434254A52 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1E97E9DA174BA64351BD3070 /* Pods_Nano_Loan.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C7FE12D6BEAF84BF0F750AB4 /* Pods */ = {
			isa = PBXGroup;
			children = (
				396BFFD982BA4DEEB82BEF6A /* Pods-Nano_Loan.debug.xcconfig */,
				BF901CC1F1C644BB2CDED1D3 /* Pods-Nano_Loan.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0457B1442DF0C6140064E008 /* Nano_Loan */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0457B15D2DF0C6170064E008 /* Build configuration list for PBXNativeTarget "Nano_Loan" */;
			buildPhases = (
				2C1CB3E73513BF6890FDAF5E /* [CP] Check Pods Manifest.lock */,
				0457B1412DF0C6140064E008 /* Sources */,
				0457B1422DF0C6140064E008 /* Frameworks */,
				0457B1432DF0C6140064E008 /* Resources */,
				12CCCE43BE04F665A0E4EAB5 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				0457B1472DF0C6140064E008 /* Nano_Loan */,
			);
			name = Nano_Loan;
			productName = Nano_Loan;
			productReference = 0457B1452DF0C6140064E008 /* Nano_Loan.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0457B13D2DF0C6140064E008 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					0457B1442DF0C6140064E008 = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 0457B1402DF0C6140064E008 /* Build configuration list for PBXProject "Nano_Loan" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0457B13C2DF0C6140064E008;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 0457B1462DF0C6140064E008 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0457B1442DF0C6140064E008 /* Nano_Loan */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0457B1432DF0C6140064E008 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		12CCCE43BE04F665A0E4EAB5 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Nano_Loan/Pods-Nano_Loan-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Nano_Loan/Pods-Nano_Loan-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Nano_Loan/Pods-Nano_Loan-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		2C1CB3E73513BF6890FDAF5E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Nano_Loan-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0457B1412DF0C6140064E008 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		0457B15E2DF0C6170064E008 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 396BFFD982BA4DEEB82BEF6A /* Pods-Nano_Loan.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Nano_Loan/Nano_Loan.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "Apple Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 353F3U78GF;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Nano_Loan/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Nano Loan";
				INFOPLIST_KEY_NSCameraUsageDescription = "Camera access is needed when capturing photos for ID checks or scanning paper documents during verification.\nThe camera will only be triggered when you actively perform a capture.";
				INFOPLIST_KEY_NSContactsUsageDescription = "We request contact access to help validate your identity by analyzing relationships that support your profile credibility.\nYour contact data is never stored or used to reach out.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Location access helps determine service availability in your region and provides local policy compliance.\nYour coordinates are retrieved just once during the application and are never stored or tracked.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Photo library access allows you to upload pre-existing files, such as IDs or utility bills, to support your application.\nNo images are accessed beyond the ones you explicitly select.";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "We use the IDFA to associate your device with your account to prevent duplicated applications and ensure security.\nNo advertising or behavioral tracking is involved.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.maxapp.mobile-test";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		0457B15F2DF0C6170064E008 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BF901CC1F1C644BB2CDED1D3 /* Pods-Nano_Loan.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Nano_Loan/Nano_Loan.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = YUQ352UGYJ;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Nano_Loan/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Nano Loan";
				INFOPLIST_KEY_NSCameraUsageDescription = "Camera access is needed when capturing photos for ID checks or scanning paper documents during verification.\nThe camera will only be triggered when you actively perform a capture.";
				INFOPLIST_KEY_NSContactsUsageDescription = "We request contact access to help validate your identity by analyzing relationships that support your profile credibility.\nYour contact data is never stored or used to reach out.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Location access helps determine service availability in your region and provides local policy compliance.\nYour coordinates are retrieved just once during the application and are never stored or tracked.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Photo library access allows you to upload pre-existing files, such as IDs or utility bills, to support your application.\nNo images are accessed beyond the ones you explicitly select.";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "We use the IDFA to associate your device with your account to prevent duplicated applications and ensure security.\nNo advertising or behavioral tracking is involved.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.moxige.testappid;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = MoXiGeProfile;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		0457B1602DF0C6170064E008 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = JXJZNTUXTP;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		0457B1612DF0C6170064E008 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = JXJZNTUXTP;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0457B1402DF0C6140064E008 /* Build configuration list for PBXProject "Nano_Loan" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0457B1602DF0C6170064E008 /* Debug */,
				0457B1612DF0C6170064E008 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0457B15D2DF0C6170064E008 /* Build configuration list for PBXNativeTarget "Nano_Loan" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0457B15E2DF0C6170064E008 /* Debug */,
				0457B15F2DF0C6170064E008 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0457B13D2DF0C6140064E008 /* Project object */;
}
