#import "LaunchViewController.h"
#import "NetworkManager.h"
#import "DynamicDomainManager.h"
#import "CustomTabBarController.h"
#import "LoginViewController.h"
#import "LoginInitModel.h"
#import "AddressDataManager.h"
#import "LocationManager.h"
#import "AppDelegate.h"
#import <CFNetwork/CFNetwork.h>
#import <SystemConfiguration/SystemConfiguration.h>
#import <AFNetworking/AFNetworkReachabilityManager.h>

// === Helper Functions ===
static BOOL NLIsProxyEnabled(void) {
    NSDictionary *proxySettings = (__bridge_transfer NSDictionary *)CFNetworkCopySystemProxySettings();
    if (!proxySettings) { return NO; }
    NSNumber *httpEnable = proxySettings[@"HTTPEnable"];
    NSNumber *httpsEnable = proxySettings[@"HTTPSEnable"];
    NSNumber *socksEnable = proxySettings[@"SOCKSEnable"];
    return (httpEnable.boolValue || httpsEnable.boolValue || socksEnable.boolValue);
}

static BOOL NLIsVPNEnabled(void) {
    NSDictionary *proxySettings = (__bridge_transfer NSDictionary *)CFNetworkCopySystemProxySettings();
    NSDictionary *scoped = proxySettings[@"__SCOPED__"];
    for (NSString *key in scoped.allKeys) {
        if ([key containsString:@"tap"] || [key containsString:@"tun"] || [key containsString:@"ppp"] || [key containsString:@"ipsec"] || [key containsString:@"utun"]) {
            return YES;
        }
    }
    return NO;
}

@interface LaunchViewController ()
@property (nonatomic, strong) AFNetworkReachabilityManager *reachabilityManager;
@property (nonatomic, assign) BOOL hasStartedInitRequest;
@property (nonatomic, assign) BOOL isDynamicDomainSwitching; // 是否正在进行动态域名切换
@end

@implementation LaunchViewController

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    // 设置基础域名（真实接口）
    [NetworkManager setBaseURL:@"https://nano.paperplane-lending.com/youtoday"];
    // 启动应用时上报位置信息
    [[LocationManager sharedManager] reportLocationInfo];

    // 每次启动都要走动态域名逻辑
    [self startDynamicDomainProcess];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 添加全屏启动背景图
    UIImageView *backgroundImageView = [[UIImageView alloc] initWithFrame:self.view.bounds];
    backgroundImageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    backgroundImageView.image = [UIImage imageNamed:@"launch_bg_image"];
    [self.view addSubview:backgroundImageView];
}



/// 使用默认域名发起登录初始化请求
- (void)requestLoginInitWithDefaultDomain {
    NSLog(@"[Launch] 使用默认域名请求初始化接口");
    [self requestLoginInitWithRetry:NO isDynamicDomain:NO];
}

/// 发起登录初始化请求，支持重试一次
- (void)requestLoginInitWithRetry:(BOOL)hasRetried isDynamicDomain:(BOOL)isDynamicDomain {
    __weak typeof(self) weakSelf = self;

    // ==== 构造初始化参数 ====
    NSString *langCode = [[NSLocale preferredLanguages] firstObject] ?: @"en";
    if (langCode.length > 2) {
        langCode = [[langCode substringToIndex:2] lowercaseString];
    }

    BOOL usingProxy = NLIsProxyEnabled();
    BOOL usingVPN = NLIsVPNEnabled();

    NSDictionary *initParams = @{@"bigger": langCode,
                                 @"ofgwendoline": @(usingProxy),
                                 @"outright": @(usingVPN)};

    // 动态域名相关接口使用10秒超时，其他接口30秒
    NSTimeInterval timeout = isDynamicDomain ? 10.0 : 10.0; // 默认域名测试也用10秒
    [NetworkManager postFormWithAPI:@"Alicia/bigger"
                              params:initParams
                     timeoutInterval:timeout
                         showLoading:NO
                          completion:^(NSDictionary *response, NSError *error) {
        if (!error && [response isKindOfClass:[NSDictionary class]]) {
            if (isDynamicDomain) {
                NSLog(@"[Launch] 动态域名初始化请求成功");
            } else {
                NSLog(@"[Launch] 默认域名初始化请求成功");
            }
            // 处理成功响应（复用逻辑）
            [weakSelf handleInitSuccessResponse:response];
        } else {
            if (isDynamicDomain) {
                // 动态域名失败，显示错误
                NSLog(@"[Launch] 动态域名初始化请求失败: %@", error.localizedDescription);
                [weakSelf showNetworkErrorAlert];
            } else {
                // 默认域名失败
                if (!hasRetried) {
                    // 第一次失败，立即重试一次
                    NSLog(@"[Launch] 默认域名初始化请求失败，立即重试");
                    [weakSelf requestLoginInitWithRetry:YES isDynamicDomain:NO];
                } else {
                    // 两次都失败，开始动态域名切换流程
                    NSLog(@"[Launch] 默认域名两次请求都失败，开始动态域名切换");
                    [weakSelf attemptDynamicDomainSwitch];
                }
            }
        }
    }];
}

#pragma mark - Dynamic Domain Process

/// 启动动态域名处理流程（每次启动都执行）
- (void)startDynamicDomainProcess {
    NSLog(@"[Launch] 启动动态域名处理流程");

    // 第一步：下载并缓存动态域名JSON
    [self downloadAndCacheDynamicDomainJSON:^(BOOL success) {
        if (success) {
            NSLog(@"[Launch] 动态域名JSON下载成功，开始测试默认接口");
        } else {
            NSLog(@"[Launch] 动态域名JSON下载失败，但继续测试默认接口");
        }

        // 第二步：无论JSON下载是否成功，都先测试默认接口
        [self testDefaultDomainAndProceed];
    }];
}

/// 下载并缓存动态域名JSON
- (void)downloadAndCacheDynamicDomainJSON:(void(^)(BOOL success))completion {
    NSLog(@"[Launch] 开始下载动态域名JSON");

    [[DynamicDomainManager sharedManager] downloadAndCacheDynamicDomainJSON:^(BOOL success, NSError * _Nullable error) {
        if (success) {
            NSLog(@"[Launch] 动态域名JSON下载并缓存成功");
        } else {
            NSLog(@"[Launch] 动态域名JSON下载失败: %@", error.localizedDescription);
        }
        if (completion) completion(success);
    }];
}

/// 测试默认域名并继续流程
- (void)testDefaultDomainAndProceed {
    NSLog(@"[Launch] 开始测试默认域名");
    // 仅在首次发起 Alicia/bigger 初始化请求前显示全局 Loading，失败不隐藏，直到成功才隐藏
    if (!self.hasStartedInitRequest) {
        self.hasStartedInitRequest = YES;
        [NetworkManager showLoading];
    }
    [self requestLoginInitWithDefaultDomain];
}

/// 尝试动态域名切换
- (void)attemptDynamicDomainSwitch {
    if (self.isDynamicDomainSwitching) {
        NSLog(@"[Launch] 动态域名切换已在进行中，跳过");
        return;
    }

    self.isDynamicDomainSwitching = YES;
    NSLog(@"[Launch] 开始动态域名切换流程");

    __weak typeof(self) weakSelf = self;
    [[DynamicDomainManager sharedManager] switchToAvailableDomainFromCache:^(BOOL success, NSString * _Nullable availableDomain, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.isDynamicDomainSwitching = NO;

            if (success && availableDomain) {
                NSLog(@"[Launch] 动态域名切换成功，新域名: %@", availableDomain);
                // 更新NetworkManager的基础域名
                [NetworkManager setBaseURL:availableDomain];
                // 使用新域名重新请求初始化接口
                [weakSelf requestLoginInitWithNewDomain];
            } else {
                NSLog(@"[Launch] 动态域名切换失败: %@", error.localizedDescription);
                // 所有域名都不可用，显示错误提示
                [weakSelf showNetworkErrorAlert];
            }
        });
    }];
}

/// 使用新域名请求初始化接口
- (void)requestLoginInitWithNewDomain {
    NSLog(@"[Launch] 使用新域名请求初始化接口");
    [self requestLoginInitWithRetry:NO isDynamicDomain:YES];
}

/// 处理初始化成功响应
- (void)handleInitSuccessResponse:(NSDictionary *)response {
    // 保存数据到model
    LoginInitModel *model = [[LoginInitModel alloc] initWithDictionary:response];
    NSData *data = [NSKeyedArchiver archivedDataWithRootObject:model requiringSecureCoding:YES error:nil];
    [[NSUserDefaults standardUserDefaults] setObject:data forKey:@"LoginInitModelCache"];

    // ===== 缓存初始化数据供全局使用 =====
    // 保存完整 JSON 到 Documents/login_init_cache.json
    NSString *jsonPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject stringByAppendingPathComponent:@"login_init_cache.json"];
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:response options:0 error:nil];
    [jsonData writeToFile:jsonPath atomically:YES];

    // 提取手机国家区号 topmost 并缓存
    NSString *topmost = response[@"awkward"][@"heardmrs"][@"topmost"];
    if ([topmost isKindOfClass:[NSString class]] && topmost.length > 0) {
        [[NSUserDefaults standardUserDefaults] setObject:topmost forKey:@"country_phone_code"];
    }

    // ===== 缓存隐私协议 URL girlswere，供登录页使用 =====
    NSString *girlswere = response[@"awkward"][@"girlswere"];
    if ([girlswere isKindOfClass:[NSString class]] && girlswere.length > 0) {
        [[NSUserDefaults standardUserDefaults] setObject:girlswere forKey:@"protocol_url"];
    }
    // ===== 解析 everyonecheered 字段，供后续位置权限引导使用 =====
    NSNumber *everyonecheered = response[@"awkward"][@"everyonecheered"];
    if ([everyonecheered respondsToSelector:@selector(integerValue)]) {
        [[NSUserDefaults standardUserDefaults] setObject:everyonecheered forKey:@"show_location_guide"];
    }
    [[NSUserDefaults standardUserDefaults] synchronize];

    // === 使用 fearlessness 字段动态初始化 Facebook SDK ===
    NSDictionary *fbConfig = response[@"awkward"][@"fearlessness"];
    AppDelegate *appDelegate = (AppDelegate *)[UIApplication sharedApplication].delegate;
    if ([fbConfig isKindOfClass:[NSDictionary class]] && [appDelegate respondsToSelector:@selector(configureFacebookSDKWithParameters:)]) {
        [appDelegate configureFacebookSDKWithParameters:fbConfig];
    }

    // 判断 modest 字段并在主线程更新 UI
    dispatch_async(dispatch_get_main_queue(), ^{
        // 初始化接口成功后，隐藏全局 Loading（仅成功时隐藏）
        [NetworkManager hideLoading];
        NSNumber *modest = response[@"modest"];
        if ([modest respondsToSelector:@selector(integerValue)] && [modest integerValue] == 0) {
            // 进入首页
            [self navigateToMainPage];
        } else {
            UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Notice" message:@"Service error, please try again later." preferredStyle:UIAlertControllerStyleAlert];
            [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
            [self presentViewController:alert animated:YES completion:nil];
        }
    });
}

/// 导航到主页面
- (void)navigateToMainPage {
    UIWindow *window = self.view.window;
    if (!window) {
        if (@available(iOS 13.0, *)) {
            for (UIWindowScene *scene in [UIApplication sharedApplication].connectedScenes) {
                if (scene.activationState == UISceneActivationStateForegroundActive) {
                    window = scene.windows.firstObject;
                    break;
                }
            }
        } else {
            window = [UIApplication sharedApplication].keyWindow;
        }
    }

    if (window) {
        CustomTabBarController *tabBarVC = [[CustomTabBarController alloc] init];
        window.rootViewController = tabBarVC;
        [UIView transitionWithView:window duration:0.3 options:UIViewAnimationOptionTransitionCrossDissolve animations:nil completion:^(BOOL finished) {
            if (finished) {
                // 预加载地址数据，方便后续选择器使用（异步执行，不阻塞UI）
                [[AddressDataManager sharedManager] fetchAddressDataIfNeededWithCompletion:nil];
            }
        }];
    } else {
        NSLog(@"[Launch] ⚠️ 无法获取 Window，无法进入主页");
    }
}

/// 显示网络错误提示
- (void)showNetworkErrorAlert {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Notice"
                                                                   message:@"Network error, please check your connection and try again."
                                                            preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
    [self presentViewController:alert animated:YES completion:nil];
}

#pragma mark - Test Methods

/// 测试动态域名切换功能（仅用于调试）
- (void)testDynamicDomainSwitch {
    NSLog(@"[Launch] 开始测试动态域名切换功能");

    // 1. 测试完整的动态域名流程
    [self startDynamicDomainProcess];
}

- (void)dealloc {
    // 确保停止网络监听
    [self.reachabilityManager stopMonitoring];
    self.reachabilityManager = nil;
    NSLog(@"[Launch] LaunchViewController dealloc");
}

@end

/*

{
  "modest": 0,
  "patted": "success",
  "awkward": {
      "roundwarily": 2,   //  1=默认印度(审核面)   2=菲律宾(用户面)  后续请求放在公参里面
      "heardmrs": {
          "topmost": "63", //手机区号
          "swallow": "http://2.412.163.251/national_flag/ph.png" //国旗logo
      },
  "everyonecheered": 1, //  1=弹出位置引导框   2=不弹 (场景：启动拒绝定位权限时，登陆后首页是否弹出位置引导弹窗，一天一次)
  "girlswere": "http://www.abidu.com", // 隐私协议
  "fearlessness": {
    // 【重要】FaceBook接入：http://47.238.207.2:3031/APP/FaceBook.git  账号：wendang  密码：wendang123
      "eventhe":"fb428921739874998",        // CFBundleURLScheme
      "gotback":"428921739874998",           // FacebookAppID
      "chicken":"PinoyLoan",    // FacebookDisplayName
      "ginger":"1a67e07ccdef7ad26a997dff1c5ab821"     // FacebookClientToke
      }
  }
}

*/
